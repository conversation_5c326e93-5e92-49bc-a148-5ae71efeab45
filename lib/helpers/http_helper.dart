import 'dart:convert';
import 'dart:io';
import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/controllers/login_controller.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/services/storage_auth_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
// import 'package:digital_cv_mobile/services/storage_auth_service.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:google_sign_in/google_sign_in.dart';
// import 'package:digital_cv_mobile/services/storage_auth_service.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import '../controllers/login_controller.dart';

/// Custom response class to maintain compatibility with existing code
class HttpResponse {
  final int statusCode;
  final dynamic data;
  final Map<String, String> headers;

  HttpResponse({
    required this.statusCode,
    required this.data,
    required this.headers,
  });
}

/// HTTP Helper to replace Dio functionality
class HttpHelper {
  static const Duration _defaultTimeout = Duration(minutes: 10);
  static final Map<String, String> _defaultHeaders = {
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
    'User-Agent': 'DigitalCV Kandidat',
    'X-Powered-By': 'DigitalCV Kandidat',
  };
  static final snackbar = Get.find<SnackBarService>();

  /// Handle unauthorized response (token expired)
  static void _handleUnauthorized() async {
    if (Get.isRegistered<LoginController>()) {
      final prefs = await SharedPreferences.getInstance();
      final isGoogle = prefs.getBool("isGoogle") ?? false;

      if (isGoogle) {
        LogService.log.i("🔄 Logout dari Google...");
        await GoogleSignIn().signOut();
      }

      LogService.log.i("🔄 Logout dari Firebase...");
      await FirebaseAuth.instance.signOut();

      await prefs.clear();
      StorageAuthService.clearLoginData();
      Get.back(); // Tutup loading dialog
      snackbar.showSuccess("controller.logout_success".tr);
      Get.offAllNamed('/login');
    } else {
      Get.offAllNamed('/login');
    }
  }

  /// Parse response body
  static dynamic parseResponse(http.Response response) {
    if (response.body.isEmpty) return null;

    try {
      return jsonDecode(response.body);
    } catch (e) {
      // If not JSON, return as string
      return response.body;
    }
  }

  /// Make GET request
  static Future<HttpResponse> get(
    String url, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
    Duration? timeout,
  }) async {
    try {
      // Build URL with query parameters
      Uri uri = Uri.parse(url);
      if (queryParameters != null && queryParameters.isNotEmpty) {
        uri = uri.replace(queryParameters: {
          ...uri.queryParameters,
          ...queryParameters
              .map((key, value) => MapEntry(key, value.toString())),
        });
      }

      final response = await http.get(
        uri,
        headers: {..._defaultHeaders, ...?headers},
      ).timeout(timeout ?? _defaultTimeout);

      final prefs = Get.find<SharedPreferences>();
      final isLoggedIn = prefs.getBool("auth") ?? false;
      final isGoogle = prefs.getBool("isGoogle") ?? false;
      if (isLoggedIn || isGoogle) {
        if (response.statusCode == 401) {
          _handleUnauthorized();
        }
      }

      return HttpResponse(
        statusCode: response.statusCode,
        data: parseResponse(response),
        headers: response.headers,
      );
    } on SocketException {
      throw 'No internet connection';
    } on http.ClientException catch (e) {
      throw 'Network error: ${e.message}';
    } catch (e) {
      throw 'Unexpected error: $e';
    }
  }

  /// Make POST request
  static Future<HttpResponse> post(
    String url, {
    dynamic data,
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
    Duration? timeout,
  }) async {
    try {
      // Build URL with query parameters
      Uri uri = Uri.parse(url);
      if (queryParameters != null && queryParameters.isNotEmpty) {
        uri = uri.replace(queryParameters: {
          ...uri.queryParameters,
          ...queryParameters
              .map((key, value) => MapEntry(key, value.toString())),
        });
      }

      // Prepare headers
      Map<String, String> requestHeaders = {..._defaultHeaders, ...?headers};

      // Prepare body
      dynamic body;
      if (data != null) {
        if (requestHeaders['Content-Type'] ==
            'application/x-www-form-urlencoded') {
          // For form data
          if (data is Map<String, dynamic>) {
            body = data.entries
                .map((e) =>
                    '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value.toString())}')
                .join('&');
          } else {
            body = data.toString();
          }
        } else {
          // For JSON data
          requestHeaders['Content-Type'] = 'application/json';
          body = jsonEncode(data);
        }
      }

      final response = await http
          .post(
            uri,
            headers: requestHeaders,
            body: body,
          )
          .timeout(timeout ?? _defaultTimeout);

      final prefs = Get.find<SharedPreferences>();
      final isLoggedIn = prefs.getBool("auth") ?? false;
      final isGoogle = prefs.getBool("isGoogle") ?? false;
      if (isLoggedIn || isGoogle) {
        if (response.statusCode == 401) {
          _handleUnauthorized();
        }
      }

      return HttpResponse(
        statusCode: response.statusCode,
        data: parseResponse(response),
        headers: response.headers,
      );
    } on SocketException {
      throw 'No internet connection';
    } on http.ClientException catch (e) {
      throw 'Network error: ${e.message}';
    } catch (e) {
      throw 'Unexpected error: $e';
    }
  }

  /// Make PUT request
  static Future<HttpResponse> put(
    String url, {
    dynamic data,
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
    Duration? timeout,
  }) async {
    try {
      // Build URL with query parameters
      Uri uri = Uri.parse(url);
      if (queryParameters != null && queryParameters.isNotEmpty) {
        uri = uri.replace(queryParameters: {
          ...uri.queryParameters,
          ...queryParameters
              .map((key, value) => MapEntry(key, value.toString())),
        });
      }

      // Prepare headers
      Map<String, String> requestHeaders = {..._defaultHeaders, ...?headers};

      // Prepare body
      dynamic body;
      if (data != null) {
        if (requestHeaders['Content-Type'] ==
            'application/x-www-form-urlencoded') {
          // For form data
          if (data is Map<String, dynamic>) {
            body = data.entries
                .map((e) =>
                    '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value.toString())}')
                .join('&');
          } else {
            body = data.toString();
          }
        } else {
          // For JSON data
          requestHeaders['Content-Type'] = 'application/json';
          body = jsonEncode(data);
        }
      }

      final response = await http
          .put(
            uri,
            headers: requestHeaders,
            body: body,
          )
          .timeout(timeout ?? _defaultTimeout);

      final prefs = Get.find<SharedPreferences>();
      final isLoggedIn = prefs.getBool("auth") ?? false;
      final isGoogle = prefs.getBool("isGoogle") ?? false;
      if (isLoggedIn || isGoogle) {
        if (response.statusCode == 401) {
          _handleUnauthorized();
        }
      }

      return HttpResponse(
        statusCode: response.statusCode,
        data: parseResponse(response),
        headers: response.headers,
      );
    } on SocketException {
      throw 'No internet connection';
    } on http.ClientException catch (e) {
      throw 'Network error: ${e.message}';
    } catch (e) {
      throw 'Unexpected error: $e';
    }
  }

  /// Make DELETE request
  static Future<HttpResponse> delete(
    String url, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
    Duration? timeout,
  }) async {
    try {
      // Build URL with query parameters
      Uri uri = Uri.parse(url);
      if (queryParameters != null && queryParameters.isNotEmpty) {
        uri = uri.replace(queryParameters: {
          ...uri.queryParameters,
          ...queryParameters
              .map((key, value) => MapEntry(key, value.toString())),
        });
      }

      final response = await http.delete(
        uri,
        headers: {..._defaultHeaders, ...?headers},
      ).timeout(timeout ?? _defaultTimeout);

      final prefs = Get.find<SharedPreferences>();
      final isLoggedIn = prefs.getBool("auth") ?? false;
      final isGoogle = prefs.getBool("isGoogle") ?? false;
      if (isLoggedIn || isGoogle) {
        if (response.statusCode == 401) {
          _handleUnauthorized();
        }
      }

      return HttpResponse(
        statusCode: response.statusCode,
        data: parseResponse(response),
        headers: response.headers,
      );
    } on SocketException {
      throw 'No internet connection';
    } on http.ClientException catch (e) {
      throw 'Network error: ${e.message}';
    } catch (e) {
      throw 'Unexpected error: $e';
    }
  }
}

/// Helper for creating HTTP clients with different base URLs
class HttpClient {
  final String baseUrl;
  final Map<String, String> defaultHeaders;

  HttpClient({
    required this.baseUrl,
    this.defaultHeaders = const {},
  });

  Future<HttpResponse> get(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
    Duration? timeout,
  }) async {
    final url = baseUrl + endpoint;
    return HttpHelper.get(
      url,
      headers: {...defaultHeaders, ...?headers},
      queryParameters: queryParameters,
      timeout: timeout,
    );
  }

  Future<HttpResponse> post(
    String endpoint, {
    dynamic data,
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
    Duration? timeout,
  }) async {
    final url = baseUrl + endpoint;
    return HttpHelper.post(
      url,
      data: data,
      headers: {...defaultHeaders, ...?headers},
      queryParameters: queryParameters,
      timeout: timeout,
    );
  }

  Future<HttpResponse> put(
    String endpoint, {
    dynamic data,
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
    Duration? timeout,
  }) async {
    final url = baseUrl + endpoint;
    return HttpHelper.put(
      url,
      data: data,
      headers: {...defaultHeaders, ...?headers},
      queryParameters: queryParameters,
      timeout: timeout,
    );
  }

  Future<HttpResponse> delete(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
    Duration? timeout,
  }) async {
    final url = baseUrl + endpoint;
    return HttpHelper.delete(
      url,
      headers: {...defaultHeaders, ...?headers},
      queryParameters: queryParameters,
      timeout: timeout,
    );
  }
}
