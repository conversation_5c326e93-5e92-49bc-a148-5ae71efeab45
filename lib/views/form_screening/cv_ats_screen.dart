import 'package:digital_cv_mobile/components/digital-cv/card_informasi_pekerjaan.dart';
import 'package:digital_cv_mobile/components/digital-cv/card_informasi_pribadi.dart';
import 'package:digital_cv_mobile/components/digital-cv/card_minat_konsep_pribadi.dart';
import 'package:digital_cv_mobile/components/digital-cv/card_pelatihan_kursus.dart';
import 'package:digital_cv_mobile/components/digital-cv/card_pengalaman_bekerja.dart';
import 'package:digital_cv_mobile/components/digital-cv/card_pengalaman_organisasi.dart';
import 'package:digital_cv_mobile/components/digital-cv/card_riwayat_pendidikan.dart';
import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/controllers/minat_konsep_controller.dart';
import 'package:digital_cv_mobile/controllers/profile_controller.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CvAtsScreen extends StatefulWidget {
  CvAtsScreen({super.key});

  @override
  State<CvAtsScreen> createState() => _CvAtsScreenState();
}

class _CvAtsScreenState extends State<CvAtsScreen> {
  final GetInfoCVController infoCVController = Get.find<GetInfoCVController>();
  final ProfileController profileController = Get.find<ProfileController>();
  final MinatKonsepController minatKonsepController =
      Get.find<MinatKonsepController>();
  bool isCollapsed = false;
  double progress = 0.6; // Contoh progress (60%)

  bool isEmpty = false;

  @override
  void initState() {
    super.initState();
    infoCVController.loadRH();
    infoCVController.loadRiwayatPekerjaan();
    infoCVController.loadRiwayatPendidikan();
    infoCVController.loadRiwayatKursus();
    infoCVController.loadRiwayatOrganisasi();
    infoCVController.loadPenguasaanBahasa();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          Positioned.fill(
            child: NotificationListener<ScrollNotification>(
              onNotification: (scrollNotification) {
                if (scrollNotification is ScrollUpdateNotification) {
                  setState(() {
                    isCollapsed = scrollNotification.metrics.pixels >
                        10; // Perubahan lebih cepat
                  });
                }
                return true;
              },
              child: RefreshIndicator(
                onRefresh: () async {
                  infoCVController.analisaCVAI.value = '';
                  infoCVController.loadRH();
                  infoCVController.loadRiwayatPekerjaan();
                  infoCVController.loadRiwayatPendidikan();
                  infoCVController.loadRiwayatKursus();
                  infoCVController.loadRiwayatOrganisasi();
                  infoCVController.loadPenguasaanBahasa();
                },
                child: CustomScrollView(
                  slivers: [
                    SliverAppBar(
                      pinned: true,
                      expandedHeight: 100.0,
                      surfaceTintColor: Colors.transparent,
                      backgroundColor: Colors.white,
                      // shape: RoundedRectangleBorder(
                      //   side: BorderSide(
                      //     color: Colors.black,
                      //   ),
                      // ),
                      forceElevated: true,
                      elevation: 3,
                      shadowColor: Colors.black,
                      iconTheme: IconThemeData(color: Colors.black),
                      title: AnimatedSwitcher(
                        duration: Duration(milliseconds: 300),
                        transitionBuilder: (child, animation) => FadeTransition(
                          opacity: animation,
                          child: child,
                        ),
                        child: isCollapsed
                            ? null // Hilangkan title saat scroll ke bawah
                            : Text(
                                "profil.cv".tr,
                                key: ValueKey('title'),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                              ),
                      ),
                      flexibleSpace: Container(
                        padding: EdgeInsets.only(
                            top: MediaQuery.of(context).padding.top),
                        alignment: Alignment.center,
                        child: AnimatedSwitcher(
                          duration: Duration(milliseconds: 300),
                          transitionBuilder: (child, animation) =>
                              FadeTransition(
                            opacity: animation,
                            child: child,
                          ),
                          child: isCollapsed
                              ? _buildCollapsedAppBar()
                              : _buildExpandedAppBar(),
                        ),
                      ),
                    ),
                    SliverToBoxAdapter(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 15, horizontal: 15),
                        child: Center(
                          child: Column(
                            mainAxisSize: MainAxisSize
                                .min, // ✅ Gunakan `min` agar tidak memaksa ukuran penuh
                            children: [
                              CardInformasiPribadi(
                                  currentRoute: "cv_ats_screen",
                                  infoCVController: infoCVController,
                                  timelineData: Utilities().timelineData,
                                  profileController: profileController),
                              SizedBox(
                                height: 10,
                              ),
                              CardRiwayatPendidikan(
                                  timelineData: Utilities().timelineData,
                                  infoCVController: infoCVController),
                              SizedBox(
                                height: 10,
                              ),
                              CardPengalamanBekerja(
                                  timelineData: Utilities().timelineData,
                                  infoCVController: infoCVController),
                              SizedBox(
                                height: 10,
                              ),
                              CardPelatihanKursus(
                                  timelineData: Utilities().timelineData,
                                  infoCVController: infoCVController),
                              SizedBox(
                                height: 10,
                              ),
                              CardPengalamanOrganisasi(
                                  timelineData: Utilities().timelineData,
                                  infoCVController: infoCVController),
                              SizedBox(
                                height: 10,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandedAppBar() {
    return SingleChildScrollView(
      // Mencegah overflow saat tinggi tidak cukup
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          key: ValueKey('expanded'),
          mainAxisSize:
              MainAxisSize.min, // Pastikan tidak mengambil semua ruang
          children: [
            SizedBox(height: 30),
            Text(
              "dcv.subjudul1".tr,
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
              softWrap: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCollapsedAppBar() {
    return Padding(
      key: ValueKey('collapsed'),
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(width: 35),
          Expanded(
            // Memastikan teks tidak menyebabkan overflow
            child: Text(
              "dcv.subjudul2".tr,
              style: TextStyle(
                fontSize: 9,
                fontWeight: FontWeight.normal,
              ),
              softWrap: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return SizedBox(
      width: isCollapsed ? 150 : MediaQuery.of(context).size.width,
      height: 5,
      child: Obx(
        () => LinearProgressIndicator(
          value: infoCVController.progress.value,
          borderRadius: BorderRadius.circular(10),
          backgroundColor: Colors.grey[300],
          color: Color(0xFF0D3B72),
        ),
      ),
    );
  }
}
